import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';

import targetTypesService from '~/shared/services/targetTypes';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export interface AgreeStatusResult {
  uidTarget: string;
  uid: string;
  agree: boolean | null;
}

export interface AgreeStatusMap {
  [targetUid: string]: {
    [targetTypeUid: string]: boolean | null;
  };
}

interface UseAgreeStatusProps {
  targets: Target[];
  currentTargetType?: TargetTypeEnum;
  enabled?: boolean;
}

export function useAgreeStatus({
  targets,
  currentTargetType,
  enabled = true,
}: UseAgreeStatusProps) {
  const targetTypeIdentifiers = useMemo(() => {
    const identifiers: Array<{ uidTarget: string; uid: string }> = [];

    targets.forEach(target => {
      if (!target.uid || !target.targetTypes) {
        return;
      }

      target.targetTypes.forEach(targetType => {
        if (!targetType.uid) {
          return;
        }

        if (currentTargetType && targetType.type !== currentTargetType) {
          return;
        }

        identifiers.push({
          uidTarget: target.uid!,
          uid: targetType.uid,
        });
      });
    });

    return identifiers;
  }, [targets, currentTargetType]);

  const {
    data: agreeStatusResults,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['agreeStatuses', targetTypeIdentifiers],
    queryFn: () => targetTypesService.getAgreeStatuses(targetTypeIdentifiers),
    enabled: enabled && targetTypeIdentifiers.length > 0,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });

  const agreeStatusMap: AgreeStatusMap = useMemo(() => {
    if (!agreeStatusResults) {
      return {};
    }

    const map: AgreeStatusMap = {};

    agreeStatusResults.forEach(result => {
      if (!map[result.uidTarget]) {
        map[result.uidTarget] = {};
      }
      map[result.uidTarget][result.uid] = result.agree;
    });

    return map;
  }, [agreeStatusResults]);

  const getTargetAgreeStatus = (
    targetUid: string,
    targetTypeUid?: string,
  ): boolean | null => {
    if (!targetUid || !agreeStatusMap[targetUid]) {
      return null;
    }

    if (targetTypeUid) {
      return agreeStatusMap[targetUid][targetTypeUid] ?? null;
    }

    const targetStatuses = agreeStatusMap[targetUid];
    const statusValues = Object.values(targetStatuses);
    return statusValues.length > 0 ? statusValues[0] : null;
  };

  const hasAgreeStatusDetermined = (targetUid: string): boolean => {
    if (!targetUid || !agreeStatusMap[targetUid]) {
      return false;
    }

    const targetStatuses = agreeStatusMap[targetUid];
    return Object.values(targetStatuses).some(status => status !== null);
  };

  const getCurrentTargetTypeAgreeStatus = (target: Target): boolean | null => {
    if (!target.uid || !currentTargetType) {
      return null;
    }

    const relevantTargetType = target.targetTypes?.find(
      tt => tt.type === currentTargetType && tt.uid,
    );

    if (!relevantTargetType?.uid) {
      return null;
    }

    return getTargetAgreeStatus(target.uid, relevantTargetType.uid);
  };

  return {
    agreeStatusMap,
    agreeStatusResults: agreeStatusResults || [],
    isLoading,
    isError,
    error,
    getTargetAgreeStatus,
    hasAgreeStatusDetermined,
    getCurrentTargetTypeAgreeStatus,
  };
}
